{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import datetime\n", "import efinance as ef\n", "from utils import mysql\n", "import pandas as pd\n", "# from data.raw_data import mins_data\n", "import requests\n", "from pqdm.processes import pqdm\n", "import datetime\n", "import adata"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calc_cost(avg_px,bm_px,side):\n", "    return  side*(avg_px/bm_px -1 )*10000\n", "def calc(mktdata,po,f):\n", "    idata=mktdata[(mktdata['time']>po['start_time'])&(mktdata['time']<=po['end_time'])]\n", "    if idata['qty'].sum()<=0:\n", "        vwap=po['filled_price']\n", "    else:\n", "        if f:\n", "            vwap=idata['amt'].sum()/idata['qty'].sum()\n", "        else:\n", "            vwap=idata['amt'].sum()/idata['qty'].sum()/100\n", "    if vwap<=0:\n", "        po.update({'vwap':0,'vwap_cost':0})\n", "        return po\n", "    vwap_cost=calc_cost(po['filled_price'],vwap,po['side'])\n", "    po.update({'vwap':vwap,'vwap_cost':vwap_cost})\n", "    return po\n", "\n", "def stats(res):\n", "    d={}\n", "    d['wavg_cost']=-(res['vwap_cost']*res['executed_notional']/res['executed_notional'].sum()).sum()\n", "    d['avg_cmp_rate']=res['comp_rate'].mean()\n", "    d['wavg_cmp_rate']=(res['comp_rate']*(res['vwap']*res['quantity'])/(res['vwap']*res['quantity']).sum()).sum()\n", "    d['exe_value']=res['executed_notional'].sum()\n", "    return d\n", "\n", "def show(datas):\n", "    l=[]\n", "    d=stats(datas)\n", "    d['group']='total'\n", "    l.append(d)\n", "    for gn in [['operation','opt'],['algo_name','algo'],['target_exe_time','exe-time'],['user','batch'],['account_id','account']]:\n", "        for n,g in datas.groupby(gn[0]):\n", "            r=stats(g)\n", "            r['group']='{}-{}'.format(gn[1],n)\n", "            l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def show2(datas):\n", "    l=[]\n", "    d=stats(datas)\n", "    d['group']='total'\n", "    l.append(d)\n", "    for gn in [['operation','opt'],['firm','user'],['account_id','account']]:\n", "        for n,g in datas.groupby(gn[0]):\n", "            r=stats(g)\n", "            r['group']='{}-{}'.format(gn[1],n)\n", "            l.append(r)\n", "    return pd.DataFrame(l)\n", "\n", "def get_klines(syms):\n", "    results=pqdm([[_] for _ in syms],adata.stock.market.get_market_min,n_jobs=10,argument_type='args')\n", "    df=pd.concat(results)\n", "    df=df.rename(columns={'stock_code':'symbol','amount':'amt','volume':'qty','trade_time':'time'})\n", "    df['amt']=df['amt'].astype(float)\n", "    df['qty']=df['qty'].astype(float)\n", "    df['time']=pd.to_datetime(df['time'])\n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 用互联网行情计算trading cost"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用efinance 数据 （现在有点问题）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date=\"********\"\n", "# date=datetime.datetime.now().strftime('%Y%m%d')\n", "# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "# df = ef.stock.get_quote_history(list(pos['symbol'].unique()), klt=1)\n", "# stk_dict={}\n", "# for k,v in df.items():\n", "#     v=v.rename(columns={'股票代码':'symbol', '日期':'time', '开盘':'open', '收盘':'close', '最高':'high', '最低':'low', '成交量':'qty', '成交额':'amt'})\n", "#     v=v[['symbol','time','open','close','high','low','qty','amt']]\n", "#     v['time']=pd.to_datetime(v['time'])\n", "#     stk_dict[k]=v\n", "# results=[]\n", "# for po in pos.to_dict('records'):\n", "#     r=calc(stk_dict[po['symbol']],po,False)\n", "#     results.append(r)\n", "# res=pd.DataFrame(results)\n", "# res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "# res['comp_rate']=res['filled_quantity']/res['quantity']\n", "# res['target_exe_time']=res['end_time']-res['start_time'] \n", "# show2(res)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date=\"********\"\n", "# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "# df=get_klines(list(pos['symbol'].unique()))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用adata 计算"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date=\"********\"\n", "date=datetime.datetime.now().strftime('%Y%m%d')\n", "pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "df=get_klines(list(pos['symbol'].unique()))\n", "stk_dict={}\n", "for k,v in df.groupby('symbol'):\n", "    stk_dict[k]=v\n", "results=[]\n", "for po in pos.to_dict('records'):\n", "    try:\n", "        r=calc(stk_dict[po['symbol']],po,True)\n", "        results.append(r)\n", "    except KeyError:\n", "        print('no data for symbol {}'.format(po['symbol']))\n", "res=pd.DataFrame(results)\n", "res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "res['comp_rate']=res['filled_quantity']/res['quantity']\n", "res['target_exe_time']=res['end_time']-res['start_time'] \n", "show2(res)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(res['account_id'].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(res['id'].unique())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 用本地L1行情计算trading cost"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from data.raw_data import mins_data\n", "# import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# def read_datas_from_zipfile(date,symbols):\n", "#     datas=mins_data.read_mindata_from_zip_file(date,1)\n", "#     datas['time']=pd.to_datetime(datas['datadate'].astype(str)+\" \"+datas['bartime']+\":00\")\n", "#     datas=datas.rename(columns={'ticker':'symbol','openprice':'open', 'closeprice':'close', 'highprice':'high', 'lowprice':'low', 'volume':'qty', 'value':'amt'})\n", "#     datas=datas[['symbol','time','open','close','high','low','qty','amt']]\n", "#     datas=datas[datas['symbol'].isin(symbols)]\n", "#     d={}\n", "#     for sym,g in datas.groupby('symbol'):\n", "#         g=g[['time','open','close','high','low','qty','amt']]\n", "#         d[sym]=g\n", "#     return d"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# date=\"20250530\"\n", "# pos=mysql.query(mysql.get_zs_trading_data_db_connection(),\"select * from algo_parentorder where date={}\".format(date))\n", "# pos=pos.fillna(0)\n", "# pos['start_time']=pd.to_datetime(pos['date'].astype(str)+pos['start_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['end_time']=pd.to_datetime(pos['date'].astype(str)+pos['end_time'].apply(lambda x:str(x).zfill(6)))\n", "# pos['side']=pos['operation'].apply(lambda x:1 if x==0 else -1)\n", "# stk_dict=read_datas_from_zipfile(date,pos['symbol'].unique())\n", "# results=[]\n", "# for po in pos.to_dict('records'):\n", "#     r=calc(stk_dict[po['symbol']],po,True)\n", "#     results.append(r)\n", "# res=pd.DataFrame(results)\n", "# res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "# res['comp_rate']=res['filled_quantity']/res['quantity']\n", "# res['target_exe_time']=res['end_time']-res['start_time'] "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# show2(res)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# df=ef.stock.get_realtime_quotes()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# results=[]\n", "# for po in pos.to_dict('records'):\n", "#     r=calc(stk_dict[po['symbol']],po,True)\n", "#     results.append(r)\n", "# res=pd.DataFrame(results)\n", "# res['executed_notional']=res['filled_price']*res['filled_quantity']\n", "# res['comp_rate']=res['filled_quantity']/res['quantity']\n", "# res['target_exe_time']=res['end_time']-res['start_time'] "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# show2(res)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}