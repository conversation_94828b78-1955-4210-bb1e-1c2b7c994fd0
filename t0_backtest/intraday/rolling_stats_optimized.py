#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的滚动周期统计分析脚本
针对大数据集进行分批处理，避免内存溢出和超时
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
import time
from datetime import datetime

def calculate_rolling_stats_batch(input_file, output_file, n=20, batch_size=100):
    """
    分批处理滚动统计，避免内存溢出
    """
    print(f"=== 优化的滚动周期统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"滚动窗口: {n}日")
    print(f"批处理大小: {batch_size}个标的")
    
    start_time = time.time()
    
    # 读取数据
    print("读取数据...")
    if input_file.endswith('.fea') or input_file.endswith('.feather'):
        df = pd.read_feather(input_file)
    else:
        df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 过滤有交易的记录
    df_traded = df[df['round_num'] > 0].copy()
    print(f"有交易记录数: {len(df_traded)}")
    
    if len(df_traded) == 0:
        print("没有交易记录，无法进行统计")
        return {}
    
    # 获取所有标的
    symbols = df_traded['code'].unique()
    print(f"标的总数: {len(symbols)}")
    
    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 分批处理
    symbol_rolling_stats = {}
    total_batches = (len(symbols) + batch_size - 1) // batch_size
    
    for batch_idx in range(total_batches):
        batch_start = batch_idx * batch_size
        batch_end = min((batch_idx + 1) * batch_size, len(symbols))
        batch_symbols = symbols[batch_start:batch_end]
        
        print(f"\n处理批次 {batch_idx + 1}/{total_batches}: 标的 {batch_start+1}-{batch_end}")
        
        # 处理当前批次的标的
        for i, symbol in enumerate(batch_symbols):
            if (i + 1) % 10 == 0:
                print(f"  处理标的 {i+1}/{len(batch_symbols)}: {symbol}")
            
            try:
                # 获取该标的的数据
                symbol_data = df_traded[df_traded['code'] == symbol].copy()
                
                if len(symbol_data) == 0:
                    continue
                
                # 计算该标的的滚动统计
                rolling_stats = calculate_symbol_rolling_stats_optimized(symbol_data, symbol, n)
                
                if len(rolling_stats) > 0:
                    symbol_rolling_stats[symbol] = rolling_stats
                    
            except Exception as e:
                print(f"    处理标的 {symbol} 时出错: {e}")
                continue
        
        # 每处理完一个批次，显示进度
        elapsed_time = time.time() - start_time
        estimated_total_time = elapsed_time * total_batches / (batch_idx + 1)
        remaining_time = estimated_total_time - elapsed_time
        
        print(f"  批次 {batch_idx + 1} 完成，已处理 {len(symbol_rolling_stats)} 个标的")
        print(f"  已耗时: {elapsed_time/60:.1f}分钟，预计剩余: {remaining_time/60:.1f}分钟")
    
    # 保存结果
    print(f"\n保存结果...")
    save_rolling_stats_optimized(symbol_rolling_stats, output_file)
    
    total_time = time.time() - start_time
    print(f"=== 滚动统计分析完成 ===")
    print(f"总耗时: {total_time/60:.1f}分钟")
    print(f"处理标的数: {len(symbol_rolling_stats)}")
    
    return symbol_rolling_stats

def calculate_symbol_rolling_stats_optimized(symbol_data, symbol, n=20):
    """
    优化的单个标的滚动统计计算
    """
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import calculate_basic_daily_stats
    
    # 按日期分组计算统计指标
    daily_stats = []
    for date, group in symbol_data.groupby('date'):
        # 简化数据转换
        group_copy = group.copy()
        group_copy['symbol'] = symbol
        group_copy['price'] = group_copy['max_amt']
        group_copy['quantity'] = 1
        group_copy['hold_amt'] = group_copy['max_amt']
        group_copy['trd_amt'] = group_copy['amt_open']
        
        stats = calculate_basic_daily_stats(group_copy)
        stats['date'] = date
        daily_stats.append(stats)
    
    if len(daily_stats) == 0:
        return pd.DataFrame()
    
    daily_stats_df = pd.DataFrame(daily_stats)
    daily_stats_df['date'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d')
    daily_stats_df = daily_stats_df.sort_values('date').reset_index(drop=True)
    
    # 计算滚动统计（只保留关键指标以节省内存）
    key_cols = ['date', 'hold_amt', 'trd_amt', 'profit', 'profit_ac', 'ret2hold_ac']
    available_cols = [col for col in key_cols if col in daily_stats_df.columns]
    df_subset = daily_stats_df[available_cols].copy()
    
    # 计算滚动统计
    rolling_stats = df_subset.rolling(window=n, min_periods=1).agg({
        'hold_amt': 'mean',
        'trd_amt': 'sum',
        'profit': 'sum',
        'profit_ac': 'sum',
        'ret2hold_ac': 'mean'
    })
    
    # 添加日期和标的列
    rolling_stats['date'] = daily_stats_df['date']
    rolling_stats['symbol'] = symbol
    
    # 重新排列列顺序
    cols = ['date', 'symbol'] + [col for col in rolling_stats.columns if col not in ['date', 'symbol']]
    rolling_stats = rolling_stats[cols]
    
    # 获取每周最后一个交易日（简化处理）
    rolling_stats['year_week'] = rolling_stats['date'].dt.strftime('%Y-%U')
    weekly_last_days = rolling_stats.groupby('year_week')['date'].max()
    
    # 筛选每周最后一个交易日的数据
    weekly_rolling_stats = rolling_stats[rolling_stats['date'].isin(weekly_last_days)].copy()
    weekly_rolling_stats = weekly_rolling_stats.drop('year_week', axis=1)
    
    return weekly_rolling_stats

def save_rolling_stats_optimized(symbol_rolling_stats, output_file):
    """
    优化的保存函数，分批写入Excel
    """
    print(f"保存结果到: {output_file}")
    
    if len(symbol_rolling_stats) == 0:
        print("⚠️  没有滚动统计数据需要保存")
        pd.DataFrame().to_excel(output_file, index=False)
        return
    
    # 根据文件扩展名保存
    if output_file.endswith('.xlsx'):
        # 保存为Excel文件，每个标的一个sheet
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                total_records = 0
                saved_count = 0
                
                for symbol, rolling_stats_df in symbol_rolling_stats.items():
                    if len(rolling_stats_df) > 0:
                        try:
                            # 格式化日期
                            rolling_stats_df_copy = rolling_stats_df.copy()
                            rolling_stats_df_copy['date'] = rolling_stats_df_copy['date'].dt.strftime('%Y%m%d')
                            
                            # 使用标的代码作为sheet名称，限制长度
                            sheet_name = str(symbol)[:31]  # Excel sheet名称最大31字符
                            rolling_stats_df_copy.to_excel(writer, sheet_name=sheet_name, index=False)
                            total_records += len(rolling_stats_df_copy)
                            saved_count += 1
                            
                            if saved_count % 100 == 0:
                                print(f"  已保存 {saved_count} 个标的...")
                                
                        except Exception as e:
                            print(f"  保存标的 {symbol} 时出错: {e}")
                            continue
                
                print(f"✅ 已保存 {saved_count} 个标的，共 {total_records} 条滚动统计记录")
        except Exception as e:
            print(f"❌ 保存Excel文件时出错: {e}")
            # 降级保存为CSV
            save_as_csv(symbol_rolling_stats, output_file.replace('.xlsx', '.csv'))
    else:
        save_as_csv(symbol_rolling_stats, output_file)

def save_as_csv(symbol_rolling_stats, output_file):
    """
    保存为CSV文件
    """
    print(f"保存为CSV文件: {output_file}")
    all_rolling_stats = []
    for symbol, rolling_stats_df in symbol_rolling_stats.items():
        if len(rolling_stats_df) > 0:
            rolling_stats_df_copy = rolling_stats_df.copy()
            rolling_stats_df_copy['date'] = rolling_stats_df_copy['date'].dt.strftime('%Y%m%d')
            all_rolling_stats.append(rolling_stats_df_copy)
    
    if all_rolling_stats:
        combined_df = pd.concat(all_rolling_stats, ignore_index=True)
        combined_df.to_csv(output_file, index=False)
        print(f"✅ 已保存 {len(combined_df)} 条滚动统计记录到CSV文件")

def main():
    """
    主函数
    """
    # 默认参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/results/final_position_100000/filtered_data_position_100000.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/final_position_100000/perf_symbol_roll_20_position_100000_optimized.xlsx"
    rolling_window = 20
    batch_size = 50  # 每批处理50个标的
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    if len(sys.argv) > 3:
        rolling_window = int(sys.argv[3])
    if len(sys.argv) > 4:
        batch_size = int(sys.argv[4])
    
    # 执行分析
    symbol_rolling_stats = calculate_rolling_stats_batch(input_file, output_file, rolling_window, batch_size)
    
    return len(symbol_rolling_stats) > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
