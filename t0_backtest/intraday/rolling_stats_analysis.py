#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
滚动周期统计分析脚本
基于tca.stats_agg_by_sym_roll_n函数，创建每周最后一个交易日节点的最近20日表现统计脚本
输出类似perf_symbol_roll_20_weipan_100000_w3.xlsx的结果
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta

# 添加tca模块路径
sys.path.append('/home/<USER>/trade/t0_backtest/intraday/tca')
import tca

def prepare_data_for_rolling_stats(df):
    """
    准备数据用于滚动统计
    """
    print("准备数据用于滚动统计...")
    
    # 导入按日统计脚本的函数
    sys.path.append('/home/<USER>/trade/t0_backtest/intraday')
    from daily_stats_analysis import transform_data_for_daily_stats, calculate_basic_daily_stats
    
    # 转换数据格式
    transformed_df = transform_data_for_daily_stats(df)
    
    # 按日期分组计算统计指标
    daily_stats = []
    
    for date, group in transformed_df.groupby('date'):
        print(f"处理日期: {date}")
        stats = calculate_basic_daily_stats(group)
        stats['date'] = date
        daily_stats.append(stats)
    
    daily_stats_df = pd.DataFrame(daily_stats)
    print(f"生成 {len(daily_stats_df)} 条日统计记录")
    
    return daily_stats_df

def get_weekly_last_trading_days(daily_stats_df):
    """
    获取每周最后一个交易日
    """
    print("识别每周最后一个交易日...")
    
    # 转换日期格式
    daily_stats_df['date'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d')
    daily_stats_df = daily_stats_df.sort_values('date')
    
    # 添加周信息
    daily_stats_df['year_week'] = daily_stats_df['date'].dt.strftime('%Y-%U')
    
    # 获取每周最后一个交易日
    weekly_last_days = daily_stats_df.groupby('year_week')['date'].max().reset_index()
    weekly_last_days.columns = ['year_week', 'last_trading_day']
    
    print(f"识别出 {len(weekly_last_days)} 个周末交易日")
    return weekly_last_days['last_trading_day'].tolist()

def calculate_rolling_stats(daily_stats_df, n=20):
    """
    计算滚动统计指标
    """
    print(f"计算滚动{n}日统计指标...")
    
    # 确保日期格式正确
    if daily_stats_df['date'].dtype == 'object':
        daily_stats_df['date'] = pd.to_datetime(daily_stats_df['date'], format='%Y%m%d')
    
    daily_stats_df = daily_stats_df.sort_values('date')
    
    try:
        # 使用tca.stats_agg_by_sym_roll_n函数
        rolling_stats = tca.stats_agg_by_sym_roll_n(daily_stats_df, n)
        return rolling_stats
    except Exception as e:
        print(f"使用tca函数计算滚动统计时出错: {e}")
        # 手动计算滚动统计
        return calculate_basic_rolling_stats(daily_stats_df, n)

def calculate_basic_rolling_stats(daily_stats_df, n=20):
    """
    手动计算基础的滚动统计指标
    """
    print(f"手动计算滚动{n}日统计指标...")
    
    # 确保数据按日期排序
    daily_stats_df = daily_stats_df.sort_values('date').reset_index(drop=True)
    
    # 选择需要的列
    required_cols = ['date', 'hold_amt', 'fee', 'trd_amt', 'turnover', 'profit', 'profit_ac', 'ret2hold', 'ret2hold_ac']
    available_cols = [col for col in required_cols if col in daily_stats_df.columns]
    df_subset = daily_stats_df[available_cols].copy()
    
    # 计算滚动统计
    rolling_stats = df_subset.rolling(window=n, min_periods=1).agg({
        'hold_amt': 'mean',
        'fee': 'sum',
        'trd_amt': 'sum',
        'turnover': 'mean',
        'profit': 'sum',
        'profit_ac': 'sum',
        'ret2hold': 'mean',
        'ret2hold_ac': 'mean'
    })
    
    # 添加日期列
    rolling_stats['date'] = daily_stats_df['date']
    
    # 重新排列列顺序
    cols = ['date'] + [col for col in rolling_stats.columns if col != 'date']
    rolling_stats = rolling_stats[cols]
    
    print(f"生成 {len(rolling_stats)} 条滚动统计记录")
    return rolling_stats

def filter_weekly_endpoints(rolling_stats_df, weekly_last_days):
    """
    筛选每周最后一个交易日的滚动统计结果
    """
    print("筛选每周最后一个交易日的滚动统计结果...")
    
    # 确保日期格式一致
    if rolling_stats_df['date'].dtype == 'object':
        rolling_stats_df['date'] = pd.to_datetime(rolling_stats_df['date'])
    
    weekly_last_days = pd.to_datetime(weekly_last_days)
    
    # 筛选每周最后一个交易日的数据
    weekly_rolling_stats = rolling_stats_df[rolling_stats_df['date'].isin(weekly_last_days)].copy()
    
    print(f"筛选出 {len(weekly_rolling_stats)} 条周末滚动统计记录")
    return weekly_rolling_stats

def save_rolling_stats(rolling_stats_df, output_file):
    """
    保存滚动统计结果
    """
    print(f"保存结果到: {output_file}")

    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)

    # 格式化日期
    rolling_stats_df_copy = rolling_stats_df.copy()
    if len(rolling_stats_df_copy) > 0 and 'date' in rolling_stats_df_copy.columns:
        # 确保日期是datetime类型
        if not pd.api.types.is_datetime64_any_dtype(rolling_stats_df_copy['date']):
            rolling_stats_df_copy['date'] = pd.to_datetime(rolling_stats_df_copy['date'])
        rolling_stats_df_copy['date'] = rolling_stats_df_copy['date'].dt.strftime('%Y%m%d')

    # 根据文件扩展名保存
    if output_file.endswith('.xlsx'):
        rolling_stats_df_copy.to_excel(output_file, index=False)
    else:
        rolling_stats_df_copy.to_csv(output_file, index=False)

    print(f"已保存 {len(rolling_stats_df)} 条滚动统计记录")

def main(input_file, output_file, rolling_window=20):
    """
    主函数
    """
    print(f"=== 滚动周期统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"滚动窗口: {rolling_window}日")
    
    # 读取数据
    print("读取数据...")
    df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 准备按日统计数据
    daily_stats_df = prepare_data_for_rolling_stats(df)
    
    # 获取每周最后一个交易日
    weekly_last_days = get_weekly_last_trading_days(daily_stats_df.copy())
    
    # 计算滚动统计
    rolling_stats_df = calculate_rolling_stats(daily_stats_df, rolling_window)
    
    # 筛选每周最后一个交易日的结果
    weekly_rolling_stats = filter_weekly_endpoints(rolling_stats_df, weekly_last_days)
    
    # 保存结果
    save_rolling_stats(weekly_rolling_stats, output_file)
    
    print("=== 滚动周期统计分析完成 ===")
    return weekly_rolling_stats

if __name__ == "__main__":
    # 默认参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/rolling_stats_sample.xlsx"
    rolling_window = 20
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    if len(sys.argv) > 3:
        rolling_window = int(sys.argv[3])
    
    # 执行分析
    rolling_stats_df = main(input_file, output_file, rolling_window)
