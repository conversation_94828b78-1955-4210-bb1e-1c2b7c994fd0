
# from test.intraday.backtest import backtest as bt
# from intraday.tca import tca
# import pandas as pd
# from data import data_reader
# from joblib import Parallel, delayed
# import os
# from pqdm.processes import pqdm
# import datetime
# from utils import stk_tools
# import pickle


from joblib import Parallel, delayed
from pqdm.processes import pqdm
import os, sys
import pandas as pd
import datetime
import pickle
from pathlib import Path


notebook_dir = Path.cwd() 
print(str(notebook_dir.parent))
# sys.path.insert(0, str(notebook_dir.parent))

print(sys.path)
from backtest import backtest as bt
from tca import tca
from tca import stk_tools

sys.path.remove(str(notebook_dir.parent))

# from data import data_reader


notebook_dir = Path.cwd() 
print(str(notebook_dir.parent))

def pickle_dump(data,pt):
    with open(pt, 'wb') as file:
        pickle.dump(data, file)

def pickle_load(pt):
    with open(pt, 'rb') as file:
        return pickle.load(file)

def run_rongshuhai_inventory(date,pords):
    try:
        open_th=0.004
        ret=bt.run_by_date(date,open_th,pords,isbt=True,rt_raw_tca=True,filt_sig_by_time=True)
        return ret
    except Exception as e:
        print("run {} failed {}".format(date,e))
        return None

# demo1
pords=bt.read_positions("/home/<USER>/py/stk_py/intraday/backtest/data/inventory/A500_rmtop20%_2024.csv",date,"sym","qty",account='test_account_w3',filt_type='csv')
# demo2
pords=bt.read_positions("/home/<USER>/py/stk_py/intraday/backtest/data/inventory/daiwenzheng_inventory_processed.csv",date,"symbol","qty",account='test_account_w3',filt_type='csv')
# demo3
df=pd.read_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/zz2000_pos_0940.csv")
df.rename(columns={"Unnamed: 0": "date"}, inplace=True)
d={}
for data in df.to_dict('records'):
    date=data['date'][:10].replace('-', '')
    if date<"********" or date>"********":
        continue
    st=datetime.datetime.strptime(data['date'], '%Y-%m-%d %H:%M:%S')
    
    print(date)
    l=[]
    for k,v in data.items():
        if k=='date':
            continue
        if v==0:
            continue
        po=bt.make_parentorder(k.split(".")[0],int(v),"test_account_w3",date,start_time=st) 
        l.append(po)
    d[date]=l
stk_tools.pickle_dump(d,"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/zz2000_pos_0940.pkl")

datas=stk_tools.pickle_load(r"/home/<USER>/py/stk_py/intraday/backtest/data/inventory/rongshuhai/hs300_pos_0935.pkl")
args=[]
for k,v in datas.items():
    args.append([k,pd.DataFrame(v)])
results=pqdm(args,run_rongshuhai_inventory,50,argument_type='args')
l=[_ for _ in results if _ is not None]
stk_tools.pickle_dump(l,"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl")
pt=r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_hs300_pos_0935.pkl"
perf_daily,perf_annually=bt.get_perf(pt)
perf_daily.to_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_daily_hs300_pos_0935.csv",index=False)
perf_annually.to_csv(r"/home/<USER>/py/stk_py/intraday/backtest/data/results/rongshuhai_perf_annually_hs300_pos_0935.csv",index=False)

