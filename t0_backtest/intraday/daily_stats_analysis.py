#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按日统计分析脚本
基于tca.stats_agg_by_date函数，创建按日统计脚本
输出类似perf_daily_weipan_100000_w3.csv的结果
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# 添加tca模块路径
sys.path.append('/home/<USER>/trade/t0_backtest/intraday/tca')
import tca

def transform_data_for_daily_stats(df):
    """
    将sample2.csv格式的数据转换为tca.stats_agg_by_date函数需要的格式
    """
    print("转换数据格式...")
    
    # 创建转换后的数据框
    transformed_df = df.copy()
    
    # 基础字段映射
    transformed_df['symbol'] = df['code']
    transformed_df['price'] = df['max_amt']  # 使用max_amt作为价格基准
    transformed_df['quantity'] = 1  # 设为1，因为已经是金额
    transformed_df['hold_amt'] = df['max_amt']
    transformed_df['trd_amt'] = df['amt_open']  # 使用开仓金额作为交易金额
    
    # 计算缺失字段
    transformed_df['cancel_ord_num'] = 0  # 设为0，因为原数据没有
    transformed_df['ord_num'] = df['trade_num']
    transformed_df['reject_ord_num'] = 0  # 设为0
    
    # 计算胜率相关字段
    transformed_df['win_round_num'] = (df['profit'] > 0).astype(int) * df['round_num']
    transformed_df['win_round_num_ac'] = (df['profit_ac'] > 0).astype(int) * df['round_num']
    
    # 计算盈利轮次的profit_ac总和
    transformed_df['profit_round_amt_ac'] = np.where(df['profit_ac'] > 0, df['profit_ac'], 0)
    
    # 计算敞口相关字段
    transformed_df['max_exposure'] = df['position']  # 使用position作为敞口
    transformed_df['max_long_exposure'] = df['long_open_amt']  # 使用多头开仓金额
    
    print(f"转换后数据形状: {transformed_df.shape}")
    return transformed_df

def calculate_daily_stats(df):
    """
    按日期分组计算统计指标
    """
    print("按日期计算统计指标...")
    
    daily_stats = []
    
    for date, group in df.groupby('date'):
        print(f"处理日期: {date}")
        
        # 使用tca.stats_agg_by_date函数计算统计指标
        try:
            stats = tca.stats_agg_by_date(group)
            stats['date'] = date
            daily_stats.append(stats)
        except Exception as e:
            print(f"计算日期 {date} 统计指标时出错: {e}")
            # 手动计算基础统计指标
            stats = calculate_basic_daily_stats(group)
            stats['date'] = date
            daily_stats.append(stats)
    
    return pd.DataFrame(daily_stats)

def calculate_basic_daily_stats(group):
    """
    手动计算基础的日统计指标
    """
    stats = {}
    
    # 基础指标
    stats['hold_amt'] = (group['price'] * group['quantity']).sum()
    stats['trd_amt'] = group['trd_amt'].sum()
    stats['fee'] = group['fee'].sum()
    stats['profit'] = group['profit'].sum()
    stats['profit_ac'] = stats['profit'] - stats['fee']
    stats['position'] = group['position'].sum()
    
    # 比率指标
    if stats['hold_amt'] > 0:
        stats['turnover'] = stats['trd_amt'] / stats['hold_amt']
        stats['ret2hold'] = stats['profit'] / stats['hold_amt']
        stats['ret2hold_ac'] = stats['profit_ac'] / stats['hold_amt']
    else:
        stats['turnover'] = 0
        stats['ret2hold'] = 0
        stats['ret2hold_ac'] = 0
    
    if stats['trd_amt'] > 0:
        stats['ret2trade'] = stats['profit'] / stats['trd_amt']
        stats['ret2trade_ac'] = stats['profit_ac'] / stats['trd_amt']
        stats['trd_rate'] = len(group[group['trd_amt'] > 0]) / len(group)
    else:
        stats['ret2trade'] = 0
        stats['ret2trade_ac'] = 0
        stats['trd_rate'] = 0
    
    # 多头相关指标
    stats['trd_amt_long'] = group['long_open_amt'].sum() + group['long_close_amt'].sum()
    stats['profit_long'] = group['profit_long'].sum()
    stats['profit_long_ac'] = stats['profit_long'] - group['fee_long'].sum()
    
    if stats['trd_amt_long'] > 0:
        stats['ret2trade_long'] = stats['profit_long'] / stats['trd_amt_long']
        stats['ret2trade_long_ac'] = stats['profit_long_ac'] / stats['trd_amt_long']
    else:
        stats['ret2trade_long'] = 0
        stats['ret2trade_long_ac'] = 0
    
    if stats['hold_amt'] > 0:
        stats['ret2hold_long'] = stats['profit_long'] / stats['hold_amt']
        stats['ret2hold_long_ac'] = stats['profit_long_ac'] / stats['hold_amt']
    else:
        stats['ret2hold_long'] = 0
        stats['ret2hold_long_ac'] = 0
    
    # 空头相关指标
    stats['trd_amt_short'] = group['short_open_amt'].sum() + group['short_close_amt'].sum()
    stats['profit_short'] = group['profit_short'].sum()
    stats['profit_short_ac'] = stats['profit_short'] - group['fee_short'].sum()
    
    if stats['trd_amt_short'] > 0:
        stats['ret2trade_short'] = stats['profit_short'] / stats['trd_amt_short']
        stats['ret2trade_short_ac'] = stats['profit_short_ac'] / stats['trd_amt_short']
    else:
        stats['ret2trade_short'] = 0
        stats['ret2trade_short_ac'] = 0
    
    if stats['hold_amt'] > 0:
        stats['ret2hold_short'] = stats['profit_short'] / stats['hold_amt']
        stats['ret2hold_short_ac'] = stats['profit_short_ac'] / stats['hold_amt']
    else:
        stats['ret2hold_short'] = 0
        stats['ret2hold_short_ac'] = 0
    
    # 轮次和胜率相关指标
    stats['round_num'] = group['round_num'].sum()
    stats['long_round_num'] = group['long_round_num'].sum()
    stats['short_round_num'] = group['short_round_num'].sum()
    stats['order_num'] = group['trade_num'].sum()
    
    # 胜率计算
    win_rounds = group[group['profit'] > 0]['round_num'].sum()
    win_rounds_ac = group[group['profit_ac'] > 0]['round_num'].sum()
    
    if stats['round_num'] > 0:
        stats['win_rate'] = win_rounds / stats['round_num']
        stats['win_rate_ac'] = win_rounds_ac / stats['round_num']
    else:
        stats['win_rate'] = 0
        stats['win_rate_ac'] = 0
    
    # 多头胜率
    long_group = group[group['long_round_num'] > 0]
    if len(long_group) > 0 and stats['long_round_num'] > 0:
        win_long = long_group[long_group['profit_long'] > 0]['long_round_num'].sum()
        win_long_ac = long_group[long_group['profit_ac_long'] > 0]['long_round_num'].sum()
        stats['win_rate_long'] = win_long / stats['long_round_num']
        stats['win_rate_long_ac'] = win_long_ac / stats['long_round_num']
    else:
        stats['win_rate_long'] = 0
        stats['win_rate_long_ac'] = 0
    
    # 空头胜率
    short_group = group[group['short_round_num'] > 0]
    if len(short_group) > 0 and stats['short_round_num'] > 0:
        win_short = short_group[short_group['profit_short'] > 0]['short_round_num'].sum()
        win_short_ac = short_group[short_group['profit_ac_short'] > 0]['short_round_num'].sum()
        stats['win_rate_short'] = win_short / stats['short_round_num']
        stats['win_rate_short_ac'] = win_short_ac / stats['short_round_num']
    else:
        stats['win_rate_short'] = 0
        stats['win_rate_short_ac'] = 0
    
    # 盈亏比计算
    win_profit = group[group['profit_ac'] > 0]['profit_ac'].mean() if len(group[group['profit_ac'] > 0]) > 0 else 0
    loss_profit = abs(group[group['profit_ac'] < 0]['profit_ac'].mean()) if len(group[group['profit_ac'] < 0]) > 0 else 1
    stats['win_loss_ratio_ac'] = win_profit / loss_profit if loss_profit > 0 else 0
    
    # 敞口相关
    stats['max_exposure'] = group['max_exposure'].max() if len(group) > 0 else 0
    stats['max_long_exposure'] = group['max_long_exposure'].max() if len(group) > 0 else 0
    
    if stats['hold_amt'] > 0:
        stats['max_exposure_rate'] = stats['max_exposure'] / stats['hold_amt']
        stats['max_long_exposure_rate'] = stats['max_long_exposure'] / stats['hold_amt']
    else:
        stats['max_exposure_rate'] = 0
        stats['max_long_exposure_rate'] = 0
    
    # 撤单率
    if stats['order_num'] > 0:
        stats['cancel_rate'] = 0  # 原数据没有撤单信息，设为0
    else:
        stats['cancel_rate'] = 0
    
    # 其他胜率指标
    stats['win_rate_sym'] = stats['win_rate_ac']  # 简化处理
    stats['win_rate_sym_ac'] = stats['win_rate_ac']
    
    # 其他盈亏比指标
    stats['win_loss_ratio'] = stats['win_loss_ratio_ac']
    stats['win_loss_ratio_long'] = stats['win_loss_ratio_ac']  # 简化处理
    stats['win_loss_ratio_long_ac'] = stats['win_loss_ratio_ac']
    stats['win_loss_ratio_short'] = stats['win_loss_ratio_ac']
    stats['win_loss_ratio_short_ac'] = stats['win_loss_ratio_ac']
    
    return stats

def save_daily_stats(daily_stats_df, output_file):
    """
    保存按日统计结果
    """
    print(f"保存结果到: {output_file}")
    
    # 确保输出目录存在
    output_dir = Path(output_file).parent
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 保存CSV文件
    daily_stats_df.to_csv(output_file, index=False)
    print(f"已保存 {len(daily_stats_df)} 条日统计记录")

def main(input_file, output_file):
    """
    主函数
    """
    print(f"=== 按日统计分析 ===")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 读取数据
    print("读取数据...")
    df = pd.read_csv(input_file)
    print(f"原始数据形状: {df.shape}")
    
    # 转换数据格式
    transformed_df = transform_data_for_daily_stats(df)
    
    # 计算按日统计
    daily_stats_df = calculate_daily_stats(transformed_df)
    
    # 保存结果
    save_daily_stats(daily_stats_df, output_file)
    
    print("=== 按日统计分析完成 ===")
    return daily_stats_df

if __name__ == "__main__":
    # 默认参数
    input_file = "/home/<USER>/trade/t0_backtest/intraday/history_tca/sample2.csv"
    output_file = "/home/<USER>/trade/t0_backtest/intraday/results/daily_stats_sample.csv"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    # 执行分析
    daily_stats_df = main(input_file, output_file)
