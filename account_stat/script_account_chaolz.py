import os, sys
from idna import encode
import pandas as pd
current_dir = os.path.dirname(os.path.abspath(__file__))

# 将项目路径添加到模块搜索路径
project_dir = os.path.abspath(os.path.join(current_dir, ".."))  # 通过..返回上一级目录
sys.path.append(project_dir)


from loguru import logger

import datetime
from misc.Readstockfile import read_remote_file, write_file
from misc.ssh_conn import sftp_clent_wintrader, ftp_clent_zx_zhongtai


def copy_files(date):
    
    src_dir = f'数据导出/中泰SmartX/{date}/'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
    
    files = [
        f'basket_{date}.csv',
        f'parentorder_{date}.csv',
        f'order_{date}.csv',
        f'transaction_{date}.csv',
        f't0_basket_{date}.csv',
        f't0_parentorder_{date}.csv',
        f't0_order_{date}.csv',
        f't0_transaction_{date}.csv',
    ]
    for file in files:
        sftp_clent_wintrader.get(os.path.join(src_dir, file), os.path.join(target_dir, file))
        
        
    src_dir = f'数据导出/中泰SmartX/自动导出/{date}/'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
    
    files = [
        'xtp_109156033251_algoList.csv',
        'xtp_109156033251_algoTickerList.csv',
        'xtp_109156033251_Asset.csv',
        'xtp_109156033251_Order.csv',
        'xtp_109156033251_Position.csv',
        'xtp_109156033251_Trade.csv',
    ]
    for file in files:
        sftp_clent_wintrader.get(os.path.join(src_dir, file), os.path.join(target_dir, file))


def filter_tuoguan_t0(date):
    file = 'xtp_109156033251_Trade.csv'
    target_file = f't0_transaction_2_{date}.csv'
    target_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    
    df = pd.read_csv(os.path.join(target_dir, file))
    df = df[df['渠道'].isna()]
    print(df.head())
    print(df.shape)
    
    df.to_csv(os.path.join(target_dir, target_file), index=False, encoding='gbk')
    
def upload_files(date):
    src_dir = f'/home/<USER>/dav/accounts/超量子中泰/客户端导出/{date}/'
    target_dir = f'chaolz_zz1000/daily_after/'
    if date not in ftp_clent_zx_zhongtai.listdir(target_dir):
        ftp_clent_zx_zhongtai.mkdir(os.path.join(target_dir, date))
    
    files = [
        f'basket_{date}.csv',
        f'parentorder_{date}.csv',
        f'order_{date}.csv',
        f'transaction_{date}.csv',
        f't0_basket_{date}.csv',
        f't0_parentorder_{date}.csv',
        f't0_order_{date}.csv',
        f't0_transaction_{date}.csv',
        f't0_transaction_2_{date}.csv',
        'xtp_109156033251_algoList.csv',
        'xtp_109156033251_algoTickerList.csv',
        'xtp_109156033251_Asset.csv',
        'xtp_109156033251_Order.csv',
        'xtp_109156033251_Position.csv',
        'xtp_109156033251_Trade.csv',
    ]
    for file in files:
        ftp_clent_zx_zhongtai.put(os.path.join(src_dir, file), os.path.join(target_dir, date, file))
    
    
if __name__ == '__main__':
    if len(sys.argv) > 1:
        date = str(sys.argv[1])
    else:
        date = datetime.datetime.now().strftime('%Y%m%d')
    
    copy_files(date)
    filter_tuoguan_t0(date)
    upload_files(date)